# Generated by Django 5.2.5 on 2025-08-30 16:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('modal', '0004_airtightnessimage'),
        ('sound_module', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='VehicleSoundInsulationData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('freq_200', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='200Hz隔声量(dB)')),
                ('freq_250', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='250Hz隔声量(dB)')),
                ('freq_315', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='315Hz隔声量(dB)')),
                ('freq_400', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='400Hz隔声量(dB)')),
                ('freq_500', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='500Hz隔声量(dB)')),
                ('freq_630', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='630Hz隔声量(dB)')),
                ('freq_800', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='800Hz隔声量(dB)')),
                ('freq_1000', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='1000Hz隔声量(dB)')),
                ('freq_1250', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='1250Hz隔声量(dB)')),
                ('freq_1600', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='1600Hz隔声量(dB)')),
                ('freq_2000', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='2000Hz隔声量(dB)')),
                ('freq_2500', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='2500Hz隔声量(dB)')),
                ('freq_3150', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='3150Hz隔声量(dB)')),
                ('freq_4000', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='4000Hz隔声量(dB)')),
                ('freq_5000', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='5000Hz隔声量(dB)')),
                ('freq_6300', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='6300Hz隔声量(dB)')),
                ('freq_8000', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='8000Hz隔声量(dB)')),
                ('freq_10000', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='10000Hz隔声量(dB)')),
                ('test_image_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='测试图片路径')),
                ('test_date', models.DateField(blank=True, null=True, verbose_name='测试日期')),
                ('test_location', models.CharField(blank=True, max_length=100, null=True, verbose_name='测试地点')),
                ('test_engineer', models.CharField(blank=True, max_length=50, null=True, verbose_name='测试工程师')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('vehicle_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='modal.vehiclemodel', verbose_name='车型')),
            ],
            options={
                'verbose_name': '车型隔声量数据',
                'verbose_name_plural': '车型隔声量数据',
                'db_table': 'vehicle_sound_insulation_data',
                'ordering': ['-created_at'],
                'unique_together': {('vehicle_model',)},
            },
        ),
    ]
