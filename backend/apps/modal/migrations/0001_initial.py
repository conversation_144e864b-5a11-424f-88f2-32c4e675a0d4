# Generated by Django 5.2.5 on 2025-08-27 06:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Component',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('component_name', models.CharField(max_length=100, verbose_name='零件名称')),
                ('category', models.CharField(max_length=100, verbose_name='分类')),
                ('component_brand', models.CharField(blank=True, max_length=100, null=True, verbose_name='零件品牌')),
                ('component_model', models.CharField(blank=True, max_length=100, null=True, verbose_name='零件规格型号')),
                ('component_code', models.CharField(max_length=50, unique=True, verbose_name='零件代码')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '零部件',
                'verbose_name_plural': '零部件',
                'db_table': 'components',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VehicleModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cle_model_code', models.CharField(max_length=50, unique=True, verbose_name='车型代码')),
                ('vehicle_model_name', models.CharField(max_length=100, verbose_name='车型名称')),
                ('vin', models.CharField(max_length=50, unique=True, verbose_name='VIN码')),
                ('drive_type', models.CharField(blank=True, max_length=30, null=True, verbose_name='驱动类型')),
                ('configuration', models.CharField(blank=True, max_length=200, null=True, verbose_name='具体配置')),
                ('production_year', models.IntegerField(blank=True, null=True, verbose_name='生产年份')),
                ('status', models.CharField(choices=[('active', '激活'), ('inactive', '未激活')], default='active', max_length=10, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '车型信息',
                'verbose_name_plural': '车型信息',
                'db_table': 'vehicle_models',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TestProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_code', models.CharField(max_length=50, unique=True, verbose_name='项目代码')),
                ('test_type', models.CharField(max_length=200, verbose_name='测试类型')),
                ('test_date', models.DateField(verbose_name='测试日期')),
                ('test_location', models.CharField(blank=True, max_length=100, null=True, verbose_name='测试地点')),
                ('test_engineer', models.CharField(max_length=50, verbose_name='测试工程师')),
                ('test_status', models.CharField(blank=True, max_length=200, null=True, verbose_name='测试状态')),
                ('excitation_method', models.CharField(blank=True, max_length=100, null=True, verbose_name='激励方式')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('component', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='modal.component', verbose_name='零件')),
                ('vehicle_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='modal.vehiclemodel', verbose_name='车辆')),
            ],
            options={
                'verbose_name': '测试项目',
                'verbose_name_plural': '测试项目',
                'db_table': 'test_projects',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModalData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('frequency', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='频率(Hz)')),
                ('damping_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='阻尼比')),
                ('mode_shape_description', models.TextField(blank=True, null=True, verbose_name='模态振型描述')),
                ('mode_shape_file', models.CharField(blank=True, max_length=255, null=True, verbose_name='GIF动图文件路径')),
                ('test_photo_file', models.CharField(blank=True, max_length=255, null=True, verbose_name='测试照片文件路径')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('updated_by', models.CharField(blank=True, max_length=50, null=True, verbose_name='修改人员')),
                ('test_project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='modal.testproject', verbose_name='测试项目')),
            ],
            options={
                'verbose_name': '模态数据',
                'verbose_name_plural': '模态数据',
                'db_table': 'modal_data',
                'ordering': ['-created_at'],
            },
        ),
    ]
