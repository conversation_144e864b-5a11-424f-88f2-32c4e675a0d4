# Generated by Django 5.2.5 on 2025-08-30 10:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('modal', '0003_airtightnesstest'),
    ]

    operations = [
        migrations.CreateModel(
            name='AirtightnessImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('front_compartment_image', models.CharField(blank=True, max_length=255, null=True, verbose_name='前舱图片路径')),
                ('door_image', models.CharField(blank=True, max_length=255, null=True, verbose_name='车门图片路径')),
                ('tailgate_image', models.CharField(blank=True, max_length=255, null=True, verbose_name='尾门图片路径')),
                ('upload_date', models.DateField(verbose_name='上传日期')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('vehicle_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='modal.vehiclemodel', verbose_name='车型')),
            ],
            options={
                'verbose_name': '气密性测试图片',
                'verbose_name_plural': '气密性测试图片',
                'db_table': 'airtightness_images',
                'ordering': ['-created_at'],
            },
        ),
    ]
