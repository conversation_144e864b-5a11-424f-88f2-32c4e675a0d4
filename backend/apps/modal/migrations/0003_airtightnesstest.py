# Generated by Django 5.2.5 on 2025-08-30 09:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('modal', '0002_alter_component_options_alter_vehiclemodel_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AirtightnessTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_date', models.DateField(verbose_name='测试日期')),
                ('test_engineer', models.CharField(max_length=50, verbose_name='测试工程师')),
                ('test_location', models.CharField(blank=True, max_length=100, null=True, verbose_name='测试地点')),
                ('uncontrolled_leakage', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='整车不可控泄漏量(SCFM)')),
                ('left_pressure_valve', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='左侧泄压阀(SCFM)')),
                ('right_pressure_valve', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='右侧泄压阀(SCFM)')),
                ('ac_circulation_valve', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='空调内外循环阀(SCFM)')),
                ('right_door_drain_hole', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='右侧门漏液孔(SCFM)')),
                ('tailgate_drain_hole', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='尾门漏液孔(SCFM)')),
                ('right_door_outer_seal', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='右侧门外水切(SCFM)')),
                ('right_door_outer_opening', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='右侧门外开(SCFM)')),
                ('side_mirrors', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='两侧外后视镜(SCFM)')),
                ('body_shell_leakage', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='白车身泄漏量(SCFM)')),
                ('other_area', models.DecimalField(blank=True, decimal_places=1, max_digits=8, null=True, verbose_name='其他区域(SCFM)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('vehicle_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='modal.vehiclemodel', verbose_name='车型')),
            ],
            options={
                'verbose_name': '气密性测试',
                'verbose_name_plural': '气密性测试',
                'db_table': 'airtightness_tests',
                'ordering': ['-created_at'],
            },
        ),
    ]
