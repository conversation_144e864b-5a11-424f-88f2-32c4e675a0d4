# Django Settings
SECRET_KEY=django-insecure-your-secret-key-here-change-in-production
DEBUG=True

# Database Configuration
DB_NAME=nvh_database
DB_USER=root
DB_PASSWORD=your-mysql-password
DB_HOST=localhost
DB_PORT=3306

# OIDC Configuration
OIDC_RP_CLIENT_ID=backend
OIDC_RP_CLIENT_SECRET=8545c061-7cf7-41e5-b92b-e6769a6a75b8
OIDC_OP_AUTHORIZATION_ENDPOINT=https://account-test.sgmw.com.cn/auth/realms/demo/protocol/openid-connect/auth
OIDC_OP_TOKEN_ENDPOINT=https://account-test.sgmw.com.cn/auth/realms/demo/protocol/openid-connect/token
OIDC_OP_USER_ENDPOINT=https://account-test.sgmw.com.cn/auth/realms/demo/protocol/openid-connect/userinfo
OIDC_OP_JWKS_ENDPOINT=https://account-test.sgmw.com.cn/auth/realms/demo/protocol/openid-connect/certs
OIDC_OP_LOGOUT_ENDPOINT=https://account-test.sgmw.com.cn/auth/realms/demo/protocol/openid-connect/logout


