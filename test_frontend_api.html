<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .result {
            margin-top: 10px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .info {
            background: #f4f4f5;
            border: 1px solid #909399;
            color: #606266;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端API测试工具</h1>
        <p>测试前端到后端的API连接，绕过Keycloak认证</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 基础连接测试</h3>
            <button class="test-button" onclick="testHealth()">健康检查</button>
            <button class="test-button" onclick="testBackendDirect()">直接访问后端</button>
            <div id="healthResult" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>2. 车型API测试（无认证）</h3>
            <button class="test-button" onclick="testVehicleModelsNoAuth()">获取车型列表（无认证）</button>
            <div id="vehicleResult" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>3. 零件API测试（无认证）</h3>
            <button class="test-button" onclick="testComponentsNoAuth()">获取零件列表（无认证）</button>
            <div id="componentResult" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>4. 代理测试</h3>
            <button class="test-button" onclick="testProxy()">测试Vite代理</button>
            <div id="proxyResult" class="result info">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        // 基础请求函数
        async function makeRequest(url, options = {}) {
            try {
                console.log(`🚀 请求: ${url}`, options);
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.text();
                let jsonData;
                try {
                    jsonData = JSON.parse(data);
                } catch (e) {
                    jsonData = { raw: data };
                }
                
                console.log(`✅ 响应: ${response.status}`, jsonData);
                return {
                    status: response.status,
                    ok: response.ok,
                    data: jsonData
                };
            } catch (error) {
                console.error(`❌ 错误:`, error);
                throw error;
            }
        }

        // 1. 健康检查
        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                // 测试后端健康检查
                const result = await makeRequest('http://127.0.0.1:8000/health/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 后端健康检查成功!\n状态码: ${result.status}\n响应: ${JSON.stringify(result.data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 后端健康检查失败: ${error.message}`;
            }
        }

        // 直接访问后端
        async function testBackendDirect() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                // 直接访问后端API
                const result = await makeRequest('http://127.0.0.1:8000/api/modal/vehicle-models/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 直接访问后端成功!\n状态码: ${result.status}\n响应: ${JSON.stringify(result.data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 直接访问后端失败: ${error.message}`;
            }
        }

        // 2. 车型API测试（无认证）
        async function testVehicleModelsNoAuth() {
            const resultDiv = document.getElementById('vehicleResult');
            resultDiv.innerHTML = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                // 通过代理访问
                const result = await makeRequest('/api/modal/vehicle-models/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 车型API测试成功!\n状态码: ${result.status}\n数据数量: ${result.data.data ? result.data.data.length : 0}\n响应: ${JSON.stringify(result.data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 车型API测试失败: ${error.message}`;
            }
        }

        // 3. 零件API测试（无认证）
        async function testComponentsNoAuth() {
            const resultDiv = document.getElementById('componentResult');
            resultDiv.innerHTML = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                // 通过代理访问
                const result = await makeRequest('/api/modal/components/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 零件API测试成功!\n状态码: ${result.status}\n数据数量: ${result.data.data ? result.data.data.length : 0}\n响应: ${JSON.stringify(result.data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 零件API测试失败: ${error.message}`;
            }
        }

        // 4. 代理测试
        async function testProxy() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.innerHTML = '测试中...';
            resultDiv.className = 'result info';
            
            try {
                // 测试代理是否工作
                const result = await makeRequest('/api/modal/vehicle-models/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ Vite代理工作正常!\n状态码: ${result.status}\n当前URL: ${window.location.origin}\n代理目标: http://127.0.0.1:8000\n响应: ${JSON.stringify(result.data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Vite代理测试失败: ${error.message}\n\n可能的原因:\n1. 后端服务器未启动\n2. 代理配置错误\n3. CORS问题`;
            }
        }

        // 页面加载时显示当前环境信息
        window.onload = function() {
            console.log('🌐 当前环境信息:');
            console.log('- 前端地址:', window.location.origin);
            console.log('- 后端地址: http://127.0.0.1:8000');
            console.log('- 代理配置: /api -> http://127.0.0.1:8000');
        };
    </script>
</body>
</html>
